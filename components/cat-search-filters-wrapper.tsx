"use client";

import { useRef } from "react";
import { CatSearch, type CatSearchRef } from "@/components/cat-search";
import { CatFilters } from "@/components/cat-filters";

export function CatSearchFiltersWrapper() {
	const searchRef = useRef<CatSearchRef>(null);

	const handleResetAll = () => {
		searchRef.current?.reset();
	};

	return (
		<div className="mb-6 space-y-4 sm:space-y-0 sm:flex sm:items-center sm:justify-between sm:gap-4">
			{/* Search Component - Full width on mobile, constrained on larger screens */}
			<div className="w-full sm:w-auto sm:flex-1 sm:max-w-md">
				<CatSearch ref={searchRef} />
			</div>

			{/* Filters Component - Positioned appropriately for each screen size */}
			<div className="w-full sm:w-auto">
				<CatFilters onResetAll={handleResetAll} />
			</div>
		</div>
	);
}
