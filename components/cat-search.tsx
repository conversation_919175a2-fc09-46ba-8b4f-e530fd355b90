"use client";

import { useState, useEffect, useImperativeHandle, forwardRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";
import { useQueryState } from "nuqs";
import { searchParser, pageParser } from "@/lib/search-params";

export interface CatSearchRef {
	reset: () => void;
}

export const CatSearch = forwardRef<CatSearchRef>((_props, ref) => {
	const t = useTranslations("cats");

	// Use nuqs for search and page state management
	const [search, setSearch] = useQueryState("search", searchParser);
	const [, setPage] = useQueryState("page", pageParser);

	// Local input state for controlled input
	const [inputValue, setInputValue] = useState(search);

	// Debounce search input for live search
	const debouncedSearch = useDebounce(inputValue, 500);

	// Handle live search with debounced input
	useEffect(() => {
		if (debouncedSearch !== search) {
			handleSearch(debouncedSearch);
		}
	}, [debouncedSearch, search]);

	const handleSearch = (value: string) => {
		const trimmedValue = value.trim();

		// Update search parameter (empty string will be handled by parser default)
		setSearch(trimmedValue || null);

		// Reset to first page when searching
		setPage(1);
	};

	const clearSearch = () => {
		setInputValue("");
		setSearch(null);
		setPage(1);
	};

	// Expose reset function via ref
	useImperativeHandle(ref, () => ({
		reset: clearSearch,
	}));

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleSearch(inputValue);
		}
	};

	return (
		<div className="relative w-full max-w-sm sm:max-w-md lg:max-w-lg">
			{/* Search Icon - Fixed positioning with proper spacing */}
			<div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none z-20 flex items-center justify-center">
				<Search className="h-4 w-4 text-muted-foreground shrink-0" />
			</div>

			{/* Input Field - Mobile-first responsive design */}
			<Input
				placeholder={t("search.placeholder")}
				value={inputValue}
				onChange={(e) => setInputValue(e.target.value)}
				onKeyDown={handleKeyDown}
				className={cn(
					// Base styles - mobile first
					"w-full min-w-0 h-10 text-sm",
					"pl-10 pr-12", // Space for icons
					"border border-input bg-background",
					"placeholder:text-muted-foreground",
					"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
					"transition-all duration-200",
					"rounded-md",
					// Small screens and up
					"sm:h-11 sm:text-base sm:pr-14",
					// Medium screens and up
					"md:text-sm",
					// Ensure proper overflow handling
					"overflow-hidden text-ellipsis"
				)}
			/>

			{/* Clear Button - Adequate touch target with proper positioning */}
			{inputValue && (
				<div className="absolute right-1 top-1/2 -translate-y-1/2 z-20 flex items-center justify-center">
					<Button
						variant="ghost"
						size="sm"
						onClick={clearSearch}
						aria-label={t("search.clear")}
						className={cn(
							// Base mobile styles - minimum 44px touch target
							"h-8 w-8 min-h-[44px] min-w-[44px] p-0",
							"rounded-md shrink-0",
							"hover:bg-muted focus:bg-muted",
							"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1",
							"transition-colors duration-200",
							"touch-manipulation",
							// Small screens and up
							"sm:h-9 sm:w-9 sm:min-h-[36px] sm:min-w-[36px]",
							// Ensure button doesn't interfere with input
							"flex items-center justify-center"
						)}
					>
						<X className="h-3.5 w-3.5 sm:h-4 sm:w-4 shrink-0" />
					</Button>
				</div>
			)}
		</div>
	);
});

CatSearch.displayName = "CatSearch";
